// The Hacker News Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
    
    // Mobile menu toggle functionality
    const menuBtn = document.querySelector('.menu-btn');
    const navMenu = document.querySelector('.nav-menu');
    
    if (menuBtn && navMenu) {
        menuBtn.addEventListener('click', function() {
            navMenu.classList.toggle('mobile-active');
        });
    }
    
    // Search functionality
    const searchBtn = document.querySelector('.search-btn');
    
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            // Add search functionality here
            console.log('Search clicked');
        });
    }
    
    // Subscribe button functionality
    const subscribeBtn = document.querySelector('.subscribe-btn');
    
    if (subscribeBtn) {
        subscribeBtn.addEventListener('click', function() {
            // Add subscription functionality here
            alert('Subscribe functionality would be implemented here');
        });
    }
    
    // Get Report button functionality
    const getReportBtn = document.querySelector('.get-report-btn');
    
    if (getReportBtn) {
        getReportBtn.addEventListener('click', function() {
            // Add report download functionality here
            alert('Report download functionality would be implemented here');
        });
    }
    
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-menu a');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            // Add active class to clicked link
            this.classList.add('active');
        });
    });

    // Article click tracking (includes both regular and horizontal articles)
    const articleLinks = document.querySelectorAll('.article-title a');

    articleLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            console.log('Article clicked:', this.textContent);
            // Allow normal navigation to proceed
        });
    });

    // Lazy loading for article images
    const articleImages = document.querySelectorAll('.article-image img');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.classList.add('loaded');
                    observer.unobserve(img);
                }
            });
        });

        articleImages.forEach(img => {
            imageObserver.observe(img);
        });
    }

    // Sidebar ad interactions
    const downloadBtn = document.querySelector('.download-btn');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            console.log('CISO Board Report download clicked');
            alert('CISO Board Report download would start here');
        });
    }

    // Zscaler ad click tracking
    const zscalerAd = document.querySelector('.zscaler-ad');
    if (zscalerAd) {
        zscalerAd.addEventListener('click', function() {
            console.log('Zscaler ad clicked');
            alert('Redirecting to Zscaler Zero Trust + AI content');
        });

        // Add cursor pointer style
        zscalerAd.style.cursor = 'pointer';
    }

    // Trending news click tracking
    const trendingLinks = document.querySelectorAll('.trending-content h4 a');
    trendingLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            console.log('Trending news clicked:', this.textContent);
            // Allow normal navigation to proceed
        });
    });

});

// Add mobile menu styles dynamically
const style = document.createElement('style');
style.textContent = `
    @media (max-width: 768px) {
        .nav-menu.mobile-active {
            display: flex;
            flex-direction: column;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .nav-menu {
            display: none;
        }
        
        .nav-menu.mobile-active a {
            border-bottom: 1px solid #e9ecef;
        }
    }
`;
document.head.appendChild(style);
