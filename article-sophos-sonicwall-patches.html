<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sophos and SonicWall Patch Critical RCE Flaws Affecting Firewalls and SMA 100 Devices</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-brand">
                <a href="index.html">CyberSecurityNews</a>
            </div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="#news">News</a>
                <a href="#analysis">Analysis</a>
                <a href="#resources">Resources</a>
                <a href="#contact">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Article Content -->
    <article class="article-page">
        <div class="container">
            <div class="article-header">
                <div class="breadcrumb">
                    <a href="index.html">Home</a> > <a href="#news">News</a> > <span>Network Security</span>
                </div>
                <h1>Sophos and SonicWall Patch Critical RCE Flaws Affecting Firewalls and SMA 100 Devices</h1>
                <div class="article-meta-full">
                    <span class="author"><i class="fas fa-user"></i> Ravie Lakshmanan</span>
                    <span class="date"><i class="fas fa-calendar"></i> July 24, 2025</span>
                    <span class="category"><i class="fas fa-tag"></i> Network Security / Vulnerability</span>
                    <span class="read-time"><i class="fas fa-clock"></i> 8 min read</span>
                </div>
            </div>

            <div class="article-image-full">
                <img src="https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400&q=80" alt="Sophos and SonicWall Security Patches">
                <div class="image-caption">Critical security vulnerabilities patched in Sophos Firewall and SonicWall SMA 100 Series devices</div>
            </div>

            <div class="article-content-full">
                <p class="lead">Sophos and SonicWall have alerted users of critical security flaws in Sophos Firewall and Secure Mobile Access (SMA) 100 Series appliances that could be exploited to achieve remote code execution.</p>

                <div class="article-image-inline">
                    <img src="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgjqkXOj8gfeIMwbA9rVA_zHeUDx9nMLNSSKWlQsg0VaB4cuOkAMu0c_dZqqctivSryAUrnN2MxGjMFonvfLoW-_mEBM91b4dy89JxaEGPL6JFgYw4auINP8OQR9TgTLDMkRRgHpqMZJLbDW8Bt-xYxnI4r8wjw-UI5q7cFvkn0y5nijBgTB3aY0MkaUvJD/s728-rw-e365/re.jpg" alt="Network Security Solutions">
                    <div class="image-caption">Advanced security solutions for protecting network infrastructure against critical vulnerabilities</div>
                </div>

                <h2>Critical Sophos Firewall Vulnerabilities</h2>
                <p>The two vulnerabilities impacting Sophos Firewall represent significant security risks that could allow attackers to achieve remote code execution under specific conditions.</p>

                <div class="article-image-inline">
                    <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80" alt="Sophos Firewall Security">
                    <div class="image-caption">Sophos Firewall vulnerabilities enable pre-authentication remote code execution</div>
                </div>

                <h3>CVE-2025-6704: SPX Arbitrary File Writing</h3>
                <ul>
                    <li><strong>CVSS Score:</strong> 9.8 (Critical)</li>
                    <li><strong>Vulnerability Type:</strong> Arbitrary file writing in Secure PDF eXchange (SPX) feature</li>
                    <li><strong>Impact:</strong> Pre-auth remote code execution</li>
                    <li><strong>Conditions:</strong> SPX enabled with High Availability (HA) mode</li>
                    <li><strong>Affected Devices:</strong> Approximately 0.05% of Sophos Firewall deployments</li>
                </ul>

                <h3>CVE-2025-7624: Legacy SMTP Proxy SQL Injection</h3>
                <ul>
                    <li><strong>CVSS Score:</strong> 9.8 (Critical)</li>
                    <li><strong>Vulnerability Type:</strong> SQL injection in legacy transparent SMTP proxy</li>
                    <li><strong>Impact:</strong> Remote code execution</li>
                    <li><strong>Conditions:</strong> Quarantining policy active and upgrade from pre-21.0 GA</li>
                    <li><strong>Affected Devices:</strong> Approximately 0.73% of Sophos Firewall deployments</li>
                </ul>

                <h2>Additional Sophos Vulnerabilities Patched</h2>
                <p>Sophos has also addressed several other critical and high-severity vulnerabilities in this security update:</p>

                <div class="article-image-inline">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80" alt="Network Security Patches">
                    <div class="image-caption">Comprehensive security patches address multiple attack vectors in Sophos Firewall</div>
                </div>

                <h3>CVE-2025-7382: WebAdmin Command Injection</h3>
                <ul>
                    <li><strong>CVSS Score:</strong> 8.8 (High)</li>
                    <li><strong>Vulnerability Type:</strong> Command injection in WebAdmin component</li>
                    <li><strong>Impact:</strong> Pre-auth code execution on HA auxiliary devices</li>
                    <li><strong>Conditions:</strong> OTP authentication enabled for admin user</li>
                </ul>

                <h3>CVE-2024-13974: Up2Date Business Logic Flaw</h3>
                <ul>
                    <li><strong>CVSS Score:</strong> 8.1 (High)</li>
                    <li><strong>Vulnerability Type:</strong> Business logic vulnerability in Up2Date component</li>
                    <li><strong>Impact:</strong> DNS environment control leading to RCE</li>
                    <li><strong>Discovered By:</strong> UK National Cyber Security Centre (NCSC)</li>
                </ul>

                <h3>CVE-2024-13973: WebAdmin SQL Injection</h3>
                <ul>
                    <li><strong>CVSS Score:</strong> 6.8 (Medium)</li>
                    <li><strong>Vulnerability Type:</strong> Post-auth SQL injection in WebAdmin</li>
                    <li><strong>Impact:</strong> Arbitrary code execution for administrators</li>
                    <li><strong>Discovered By:</strong> UK National Cyber Security Centre (NCSC)</li>
                </ul>

                <h2>Affected Sophos Firewall Versions</h2>
                <p>The vulnerabilities affect different versions of Sophos Firewall, with some impacting older versions and others affecting more recent releases:</p>

                <div class="article-image-inline">
                    <img src="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80" alt="Version Management">
                    <div class="image-caption">Multiple Sophos Firewall versions affected across different vulnerability classes</div>
                </div>

                <h3>Version Impact Matrix</h3>
                <ul>
                    <li><strong>CVE-2024-13974:</strong> Affects Sophos Firewall v21.0 GA (21.0.0) and older</li>
                    <li><strong>CVE-2024-13973:</strong> Affects Sophos Firewall v21.0 GA (21.0.0) and older</li>
                    <li><strong>CVE-2025-6704:</strong> Affects Sophos Firewall v21.5 GA (21.5.0) and older</li>
                    <li><strong>CVE-2025-7624:</strong> Affects Sophos Firewall v21.5 GA (21.5.0) and older</li>
                    <li><strong>CVE-2025-7382:</strong> Affects Sophos Firewall v21.5 GA (21.5.0) and older</li>
                </ul>

                <h2>SonicWall SMA 100 Series Critical Vulnerability</h2>
                <p>The disclosure comes as SonicWall detailed a critical bug in the SMA 100 Series web management interface that poses significant risks to enterprise remote access infrastructure.</p>

                <div class="article-image-inline">
                    <img src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80" alt="SonicWall SMA Security">
                    <div class="image-caption">SonicWall SMA 100 Series devices vulnerable to arbitrary file upload attacks</div>
                </div>

                <h3>CVE-2025-40599: Arbitrary File Upload</h3>
                <ul>
                    <li><strong>CVSS Score:</strong> 9.1 (Critical)</li>
                    <li><strong>Vulnerability Type:</strong> Arbitrary file upload in web management interface</li>
                    <li><strong>Impact:</strong> Remote code execution</li>
                    <li><strong>Prerequisites:</strong> Administrative privileges required</li>
                    <li><strong>Affected Products:</strong> SMA 210, 410, 500v</li>
                    <li><strong>Fixed Version:</strong> 10.2.2.1-90sv</li>
                </ul>

                <h2>OVERSTEP Backdoor Connection</h2>
                <p>SonicWall also pointed out that while the vulnerability has not been exploited, there exists a potential risk in light of a recent report from the Google Threat Intelligence Group (GTIG), which found evidence of a threat actor dubbed UNC6148 leveraging fully-patched SMA 100 series devices to deploy a backdoor called OVERSTEP.</p>

                <div class="article-image-inline">
                    <img src="https://images.unsplash.com/photo-1614064641938-3bbee52942c7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80" alt="Advanced Persistent Threats">
                    <div class="image-caption">UNC6148 threat actor deploys OVERSTEP backdoor on fully-patched SMA devices</div>
                </div>

                <h3>UNC6148 Threat Actor Profile</h3>
                <ul>
                    <li><strong>Target Focus:</strong> SonicWall SMA 100 Series devices</li>
                    <li><strong>Attack Method:</strong> Exploitation of fully-patched systems</li>
                    <li><strong>Malware:</strong> OVERSTEP backdoor deployment</li>
                    <li><strong>Discovery:</strong> Google Threat Intelligence Group (GTIG)</li>
                    <li><strong>Implications:</strong> Advanced persistent threat capabilities</li>
                </ul>

                <h2>Immediate Remediation Steps</h2>
                <p>Organizations using affected Sophos and SonicWall devices should take immediate action to apply security patches and implement additional protective measures.</p>

                <h3>Sophos Firewall Remediation</h3>
                <ul>
                    <li><strong>Apply Security Updates:</strong> Install latest Sophos Firewall patches immediately</li>
                    <li><strong>Review Configurations:</strong> Assess SPX and SMTP proxy settings</li>
                    <li><strong>Monitor Logs:</strong> Check for signs of exploitation attempts</li>
                    <li><strong>Validate HA Setup:</strong> Review High Availability configurations</li>
                    <li><strong>Update Documentation:</strong> Record configuration changes and patches</li>
                </ul>

                <h3>SonicWall SMA 100 Remediation</h3>
                <ul>
                    <li><strong>Disable Remote Management:</strong> Remove external-facing interface (X1) access</li>
                    <li><strong>Reset Credentials:</strong> Change all passwords and reinitialize OTP binding</li>
                    <li><strong>Enable MFA:</strong> Enforce multi-factor authentication for all users</li>
                    <li><strong>Deploy WAF:</strong> Enable Web Application Firewall on SMA 100</li>
                    <li><strong>Review Logs:</strong> Check appliance logs for anomalies</li>
                </ul>

                <h2>SMA 500v Virtual Product Specific Steps</h2>
                <p>Organizations using the SMA 500v virtual product require additional remediation steps due to the virtual nature of the deployment:</p>

                <div class="article-image-inline">
                    <img src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80" alt="Virtual Infrastructure">
                    <div class="image-caption">SMA 500v virtual appliances require complete reinstallation for security</div>
                </div>

                <h3>Virtual Appliance Remediation Process</h3>
                <ol>
                    <li><strong>Backup OVA File:</strong> Create backup of current virtual appliance</li>
                    <li><strong>Export Configuration:</strong> Save current device configuration</li>
                    <li><strong>Remove VM:</strong> Delete existing virtual machine and associated disks</li>
                    <li><strong>Clean Snapshots:</strong> Remove all virtual disk snapshots</li>
                    <li><strong>Install New OVA:</strong> Deploy updated OVA from SonicWall</li>
                    <li><strong>Restore Configuration:</strong> Apply saved configuration to new instance</li>
                </ol>

                <h2>Security Monitoring and Detection</h2>
                <p>Organizations should implement comprehensive monitoring to detect potential exploitation attempts and unauthorized access:</p>

                <h3>Monitoring Recommendations</h3>
                <ul>
                    <li><strong>Log Analysis:</strong> Review connection history for anomalies</li>
                    <li><strong>Behavioral Monitoring:</strong> Watch for unusual administrative activities</li>
                    <li><strong>Network Traffic Analysis:</strong> Monitor for suspicious communication patterns</li>
                    <li><strong>File Integrity Monitoring:</strong> Track changes to critical system files</li>
                    <li><strong>Authentication Monitoring:</strong> Log all authentication attempts and failures</li>
                </ul>

                <h2>Industry Impact and Recommendations</h2>
                <p>These vulnerabilities highlight the critical importance of maintaining current security patches for network infrastructure devices and implementing defense-in-depth strategies.</p>

                <div class="article-image-inline">
                    <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80" alt="Enterprise Security Strategy">
                    <div class="image-caption">Comprehensive security strategy essential for protecting network infrastructure</div>
                </div>

                <h3>Long-term Security Strategies</h3>
                <ul>
                    <li><strong>Patch Management:</strong> Establish systematic vulnerability management processes</li>
                    <li><strong>Network Segmentation:</strong> Implement proper network isolation and access controls</li>
                    <li><strong>Zero Trust Architecture:</strong> Deploy comprehensive identity and access management</li>
                    <li><strong>Threat Intelligence:</strong> Monitor for emerging threats and attack patterns</li>
                    <li><strong>Incident Response:</strong> Maintain robust incident response capabilities</li>
                </ul>

                <h2>Weekly Security Landscape: Broader Context</h2>
                <p>These Sophos and SonicWall vulnerabilities are part of a broader pattern of sophisticated attacks targeting network infrastructure. Recent security intelligence reveals that threat actors are increasingly focusing on legitimate-looking attack vectors that exploit trust relationships and signed software.</p>

                <h3>SharePoint Attacks Highlight Infrastructure Risks</h3>
                <p>The recent Microsoft SharePoint attacks attributed to Chinese hacking groups (Linen Typhoon, Violet Typhoon, and Storm-2603) demonstrate how attackers are targeting enterprise infrastructure at scale. Over 400 organizations globally were compromised through CVE-2025-49706 and CVE-2025-49704, collectively called ToolShell exploits.</p>

                <p>These attacks highlight a concerning trend: threat actors are moving beyond traditional perimeter defenses to target the very systems organizations rely on for collaboration and data sharing. The investigation into whether Microsoft's Active Protections Program (MAPP) may have inadvertently provided early access to vulnerability information underscores the complex security challenges facing even the most security-conscious organizations.</p>

                <h3>Supply Chain and Trust-Based Attacks</h3>
                <p>Security researchers are observing a shift toward attacks that leverage legitimate channels and trusted relationships. Recent campaigns include:</p>
                <ul>
                    <li><strong>North Korean IT Worker Schemes:</strong> Sophisticated operations using AI-enhanced photos, deepfakes, and stolen identities to infiltrate U.S. companies, generating $17 million in illicit funds while potentially planting malware for espionage</li>
                    <li><strong>Cloud Infrastructure Targeting:</strong> Soco404 and Koske malware campaigns targeting misconfigured cloud instances to deploy cryptocurrency miners across both Linux and Windows systems</li>
                    <li><strong>Browser-Based Attacks:</strong> The Coyote banking trojan becoming the first known malware to exploit Windows UI Automation framework for credential harvesting</li>
                </ul>

                <h3>Critical Vulnerability Trends</h3>
                <p>This week's high-risk vulnerabilities extend beyond network appliances to include:</p>
                <ul>
                    <li><strong>Cisco ISE Active Exploits:</strong> CVE-2025-20281, CVE-2025-20337, and CVE-2025-20282 allowing root-level code execution</li>
                    <li><strong>VMware Tools Vulnerabilities:</strong> CVE-2025-22230 and CVE-2025-22247 enabling privilege escalation</li>
                    <li><strong>Cloud Service Flaws:</strong> AWS Client VPN and various enterprise platforms showing continued exposure risks</li>
                </ul>

                <h2>Conclusion</h2>
                <p>The simultaneous disclosure of critical vulnerabilities in both Sophos Firewall and SonicWall SMA 100 Series devices underscores the ongoing challenges facing network security infrastructure. With CVSS scores reaching 9.8, these vulnerabilities represent significant risks that require immediate attention from security teams.</p>

                <p>The connection to the UNC6148 threat actor and OVERSTEP backdoor demonstrates that even fully-patched systems can be at risk from sophisticated adversaries. In the current threat landscape, where attacks increasingly leverage legitimate channels and trusted relationships, organizations must implement comprehensive security measures that go beyond patching to include proper configuration management, monitoring, and incident response capabilities.</p>

                <p><strong>The clearest threats today aren't always the loudest—they're often the most legitimate-looking.</strong> As identity, trust, and tooling become increasingly interlinked, security teams must defend not just against intrusions, but against trust itself being weaponized by sophisticated threat actors.</p>

                <p>The specific remediation requirements for virtual appliances highlight the complexity of modern network infrastructure and the need for tailored security approaches based on deployment models. Organizations should prioritize these patches and implement the recommended security measures to protect against potential exploitation.</p>

                <div class="article-footer">
                    <div class="tags">
                        <span class="tag">Sophos</span>
                        <span class="tag">SonicWall</span>
                        <span class="tag">RCE</span>
                        <span class="tag">Firewall</span>
                        <span class="tag">SMA 100</span>
                        <span class="tag">OVERSTEP</span>
                        <span class="tag">UNC6148</span>
                    </div>
                    <div class="share-buttons">
                        <a href="#" class="share-btn"><i class="fab fa-twitter"></i> Share</a>
                        <a href="#" class="share-btn"><i class="fab fa-linkedin"></i> Share</a>
                        <a href="#" class="share-btn"><i class="fas fa-link"></i> Copy Link</a>
                    </div>
                </div>
            </div>
        </div>
    </article>

    <!-- Related Articles -->
    <section class="related-articles">
        <div class="container">
            <h3>Related Articles</h3>
            <div class="related-grid">
                <div class="related-item">
                    <img src="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEioFgy77Nhemnu-J7tFZrPrNWqU3YYisg8msYMStMHGEBzrWptx-dT1VQk4w73r9WhQoH54Q_hRY5EIdQmhRrim0I4e-QFEQspo1kEw27WC2b85GfuprMjxYzp38XcBb_msHql1-8EAURCMzFPKcvfkJmIDBctcKdQq2xnaxBG1tQcJ_GMFWhVv5q-Y_HuD/s728-rw-e365/phone-hack.jpg" alt="Mitel Flaw">
                    <h4><a href="article-critical-mitel-flaw.html">Critical Mitel Flaw Lets Hackers Bypass Login</a></h4>
                </div>
                <div class="related-item">
                    <img src="https://images.unsplash.com/photo-1581092160562-40aa08e78837?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=120&q=80" alt="Niagara Framework">
                    <h4><a href="article-niagara-framework-flaws.html">Critical Niagara Framework Vulnerabilities</a></h4>
                </div>
                <div class="related-item">
                    <img src="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhYZ60wB_icpHieyTLBJ566xZlckQVoTMev7-MZkWaU821aXxk-AAieNfUraU_LA0W7A0ScxK9Q4duLGqI1tWXudaMGv7WeY4XoT5k5bclDjBKtJQcGrMlyL1mhk405UxtnFqfbiX-G1gpsiF0d1mNOCDa4ClcFuiwe1il4DozFiYw4xE0p7kipNUS0rI-m/s728-rw-e365/vmware.jpg" alt="Fire Ant VMware">
                    <h4><a href="article-fire-ant-vmware-exploits.html">Fire Ant Exploits VMware Infrastructure</a></h4>
                </div>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
</body>
</html>
