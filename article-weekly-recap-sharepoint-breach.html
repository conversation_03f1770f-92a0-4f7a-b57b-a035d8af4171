<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ Weekly Recap — SharePoint Breach, Spyware, IoT Hijacks, DPR<PERSON>aud, Crypto Drains and More</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-brand">
                <a href="index.html">CyberSecurityNews</a>
            </div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="#news">News</a>
                <a href="#analysis">Analysis</a>
                <a href="#resources">Resources</a>
                <a href="#contact">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Article Header -->
    <section class="article-header">
        <div class="container">
            <div class="article-meta">
                <span class="category">Weekly Recap / Security News</span>
                <span class="date"><i class="fas fa-calendar"></i> Jul 28, 2025</span>
                <span class="author"><i class="fas fa-user"></i> Ravie Lakshmanan</span>
            </div>
            <h1 class="article-title">⚡ Weekly Recap — SharePoint Breach, Spyware, IoT Hijacks, DPRK Fraud, Crypto Drains and More</h1>
            <p class="article-subtitle">Some risks don't breach the perimeter—they arrive through signed software, clean resumes, or sanctioned vendors still hiding in plain sight.</p>
        </div>
    </section>

    <!-- Article Content -->
    <section class="article-content-section">
        <div class="container">
            <div class="article-content-full">
                <div class="article-image-inline">
                    <img src="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgjqkXOj8gfeIMwbA9rVA_zHeUDx9nMLNSSKWlQsg0VaB4cuOkAMu0c_dZqqctivSryAUrnN2MxGjMFonvfLoW-_mEBM91b4dy89JxaEGPL6JFgYw4auINP8OQR9TgTLDMkRRgHpqMZJLbDW8Bt-xYxnI4r8wjw-UI5q7cFvkn0y5nijBgTB3aY0MkaUvJD/s728-rw-e365/re.jpg" alt="Weekly Security Recap">
                    <div class="image-caption">Weekly cybersecurity threats and trends analysis</div>
                </div>

                <p class="lead">This week, the clearest threats weren't the loudest—they were the most legitimate-looking. In an environment where identity, trust, and tooling are all interlinked, the strongest attack path is often the one that looks like it belongs. Security teams are now challenged to defend systems not just from intrusions—but from trust itself being turned into a weapon.</p>

                <h2>⚡ Threat of the Week</h2>
                <h3>Microsoft SharePoint Attacks Traced to China</h3>
                <p>The fallout from an attack spree targeting defects in on-premises Microsoft SharePoint servers continues to spread a week after the discovery of the zero-day exploits, with more than 400 organizations globally compromised. The attacks have been attributed to two known Chinese hacking groups tracked as Linen Typhoon (aka APT27), Violet Typhoon (aka APT31), and a suspected China-based threat actor codenamed Storm-2603 that has leveraged the access to deploy Warlock ransomware.</p>

                <p>The attacks leverage CVE-2025-49706, a spoofing flaw, and CVE-2025-49704, a remote code execution bug, collectively called ToolShell. Bloomberg reported that Microsoft is investigating whether a leak from Microsoft Active Protections Program (MAPP), which provides early access to vulnerability information to security software providers, may have led to the zero-day exploitation. China has denied allegations it was behind the campaign.</p>

                <h2>🔔 Top News</h2>
                
                <h3>U.S. Treasury Sanctions N. Korean Company for IT Worker Scheme</h3>
                <p>The U.S. Department of the Treasury's Office of Foreign Assets Control (OFAC) sanctioned a North Korean front company and three associated individuals for their involvement in the fraudulent remote information technology (IT) worker scheme designed to generate illicit revenues for Pyongyang.</p>

                <p>In a related move, Christina Marie Chapman, a laptop farmer in Arizona responsible for facilitating the scheme, was sentenced to jail for eight-and-a-half years, after raising $17 million in illicit funds for the regime. In these schemes, IT workers from North Korea use well-crafted, carefully curated portfolios, complete with full social media profiles, AI-enhanced photos and deepfakes, and stolen identities to pass background checks and land jobs at various U.S. companies.</p>

                <h3>Soco404 and Koske Target Misconfigured Cloud Instances</h3>
                <p>Two different malware campaigns have targeted vulnerabilities and misconfigurations across cloud environments to deliver cryptocurrency miners. These activity clusters have been codenamed Soco404 and Koske. While Soco404 targets both Linux and Windows systems to deploy platform-specific malware, Koske is a Linux-focused threat.</p>

                <p>There is also evidence to suggest that Koske has been developed using a large language model (LLM), given the presence of well-structured comments, best-practice logic flow with defensive scripting habits, and synthetic panda-related imagery to host the miner payload.</p>

                <h3>XSS Forum Taken Down and Suspected Admin Arrested</h3>
                <p>Law enforcement notched a significant victory against the cybercrime economy with the disruption of the notorious forum XSS and the arrest of its suspected administrator. That said, it's important to note that takedowns of similar forums have proved short-lived, and threat actors often move to new platforms or other alternatives, such as Telegram channels.</p>

                <h3>Coyote Trojan Exploits Windows UI Automation</h3>
                <p>The Windows banking trojan known as Coyote has become the first known malware strain to exploit the Windows accessibility framework called UI Automation (UIA) to harvest sensitive information. Coyote, which is known to target Brazilian users, comes with capabilities to log keystrokes, capture screenshots, and serve overlays on top of login pages associated with financial enterprises.</p>

                <h3>Cisco Confirms Active Exploits Targeting ISE</h3>
                <p>Cisco has warned that a set of security flaws in Identity Services Engine (ISE) and ISE Passive Identity Connector (ISE-PIC) have come under active exploitation in the wild. The flaws, CVE-2025-20281, CVE-2025-20337, and CVE-2025-20282, allow an attacker to execute arbitrary code on the underlying operating system as root or upload arbitrary files to an affected device.</p>

                <h2>‎️‍🔥 Trending CVEs</h2>
                <p>Hackers are quick to jump on newly discovered software flaws – sometimes within hours. Whether it's a missed update or a hidden bug, even one unpatched CVE can open the door to serious damage. Below are this week's high-risk vulnerabilities making waves.</p>

                <div class="cve-list">
                    <ul>
                        <li><strong>CVE-2025-54068</strong> (Laravel Livewire Framework)</li>
                        <li><strong>CVE-2025-34300</strong> (Lighthouse Studio)</li>
                        <li><strong>CVE-2025-6704, CVE-2025-7624</strong> (Sophos Firewall)</li>
                        <li><strong>CVE-2025-40599</strong> (SonicWall SMA 100 Series)</li>
                        <li><strong>CVE-2025-22230, CVE-2025-22247</strong> (Broadcom VMware Tools)</li>
                        <li><strong>CVE-2025-8069</strong> (AWS Client VPN for Windows)</li>
                        <li><strong>CVE-2025-24000</strong> (Post SMTP)</li>
                    </ul>
                </div>

                <h2>📰 Around the Cyber World</h2>
                
                <h3>Google Removes 1000s of YouTube Channels Tied to Influence Ops</h3>
                <p>Google removed nearly 11,000 YouTube channels and other accounts tied to state-linked propaganda campaigns from China, Russia and more in the second quarter of 2025. It removed over 2,000 removed channels linked to Russia, including 20 YouTube channels, 4 Ads accounts, and 1 Blogger blog associated with RT, a Russian state-controlled media outlet.</p>

                <h3>U.K. Sanctions 3 Russian GRU Units for Sustained Cyber Attacks</h3>
                <p>The U.K. sanctioned three units of the Russian military intelligence agency (GRU) and 18 military intelligence officers for "conducting a sustained campaign of malicious cyber activity over many years" with an aim to "sow chaos, division and disorder in Ukraine and across the world."</p>

                <h3>U.K. Floats Ransomware Payments Ban for Public Bodies</h3>
                <p>The U.K. government has proposed new legislation that would ban public sector organizations and critical national infrastructure from paying criminal operators behind ransomware attacks, as well as enforce mandatory reporting requirements for all victims to inform law enforcement of attacks.</p>

                <h2>🔒 Tip of the Week</h2>
                <h3>Don't Trust Your Browser Blindly</h3>
                <p>Most people think of their browser as just a tool to get online — but in reality, it's one of the most exposed parts of your device. Behind the scenes, your browser quietly stores names, emails, companies, and sometimes even payment info. This data often lives in plain, unencrypted files that are easy to extract if someone gains local access — even briefly.</p>

                <p>For example, in Chrome or Edge, personal autofill details are stored in a file called Web Data, which is a basic SQLite database anyone with access can read. This means that if your machine is compromised — even by a simple script — your personal or even work identity can be quietly stolen.</p>

                <h4>Here's how to reduce the risk:</h4>
                <ul>
                    <li>Clear autofill, cookies, and site data regularly</li>
                    <li>Disable autofill entirely on workstations</li>
                    <li>Limit extensions — audit them using tools like CRXcavator or Extension Police</li>
                    <li>Use DB Browser for SQLite to inspect stored files (Web Data, Cookies)</li>
                    <li>Use tools like BleachBit to securely wipe traces</li>
                </ul>

                <h2>Conclusion</h2>
                <p>This week's signals are less a conclusion and more a provocation: What else might we be misclassifying? What familiar data could become meaningful under a different lens? If the adversary thinks in systems, not symptoms, our defenses must evolve accordingly.</p>

                <p>Sometimes, the best response isn't a patch—it's a perspective shift. There's value in looking twice where others have stopped looking altogether.</p>

                <!-- Related Articles -->
                <div class="related-articles">
                    <h3>Related Articles</h3>
                    <div class="related-grid">
                        <a href="article-sophos-sonicwall-patches.html" class="related-item">
                            <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200&q=80" alt="Sophos SonicWall">
                            <h4>Sophos and SonicWall Patch Critical RCE Flaws</h4>
                        </a>
                        <a href="article-castleloader-malware.html" class="related-item">
                            <img src="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEj7HzHyahYXFo7TTcJh4x17_uOBaLvndXF4VtHRyDHLwT-edB5wsprqJWL8xEup6FD1vtE6M6H90fDAzYIndJ4XUe2xfl10cl5avgpvTEcDZIOFOwHY6Aveu16JzkFiNFxMo_tyKQ8MXFlr9j-YbFbOBX4rAb3vzM0h-UuTJAHFck5XAehV5CkIcGFhOFE1/s2600/prodaft.jpg" alt="CastleLoader">
                            <h4>CastleLoader Malware Infects 469 Devices</h4>
                        </a>
                        <a href="article-chinese-genai-risks.html" class="related-item">
                            <img src="https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200&q=80" alt="GenAI Risks">
                            <h4>Overcoming Risks from Chinese GenAI Tool Usage</h4>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
</body>
</html>
